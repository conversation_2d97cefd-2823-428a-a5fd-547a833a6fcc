# 🦊 Auto Proxy Functionality for Webscout OpenAI Providers

This document describes the automatic proxy management system integrated into Webscout's OpenAI-compatible providers.

## Overview

The auto proxy system provides:
- **Automatic proxy fetching** using the ProxyFox library
- **Proxy pool management** with health checking and rotation
- **Seamless integration** with existing providers
- **Failure handling** and automatic proxy replacement
- **Country and speed filtering** for proxy selection

## Installation

### Required Dependencies

```bash
# Install ProxyFox for automatic proxy fetching
pip install proxyfox
```

### Optional: Custom Proxy Lists

You can also use custom proxy lists without ProxyFox.

## Configuration

### Server Configuration

Add proxy settings to your API server configuration:

```python
from webscout.Provider.OPENAI.api import config

# Enable proxy functionality
config.proxy_enabled = True
config.proxy_auto_rotate = True
config.proxy_pool_size = 10
config.proxy_protocol = "http"  # "http", "https", or None
config.proxy_country = "US"  # Optional country filter
config.proxy_max_speed_ms = 2000  # Optional speed filter
config.proxy_custom_list = [  # Optional custom proxy list
    "http://proxy1.example.com:8080",
    "http://proxy2.example.com:8080"
]
```

### Programmatic Configuration

```python
from webscout.Provider.OPENAI.proxy_manager import ProxyConfig, ProxyManager

# Create proxy configuration
config = ProxyConfig(
    enabled=True,
    auto_rotate=True,
    rotation_interval=300,  # seconds
    pool_size=10,
    protocol="http",
    country="US",
    max_speed_ms=2000,
    health_check_interval=60,
    custom_proxies=["http://proxy.example.com:8080"]
)

# Initialize proxy manager
manager = ProxyManager(config)
```

## Usage

### Basic Usage with Providers

```python
from webscout.Provider.OPENAI.deepinfra import DeepInfra

# Initialize provider
client = DeepInfra()

# Make request with auto proxy
response = client.chat.completions.create(
    model="deepseek-ai/DeepSeek-R1-Turbo",
    messages=[{"role": "user", "content": "Hello!"}],
    auto_proxy=True,  # Enable auto proxy
    proxy_rotation=True  # Use random proxy selection
)
```

### API Endpoint Usage

```bash
# Chat completion with auto proxy
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "DeepInfra/deepseek-ai/DeepSeek-R1-Turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "auto_proxy": true,
    "proxy_rotation": true
  }'
```

### Monitoring Proxy Status

```bash
# Get proxy status
curl "http://localhost:8000/v1/proxy/status"

# Refresh proxy pool
curl -X POST "http://localhost:8000/v1/proxy/refresh"
```

## Configuration Options

### ProxyConfig Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enabled` | bool | False | Enable/disable proxy functionality |
| `auto_rotate` | bool | True | Enable automatic proxy rotation |
| `rotation_interval` | int | 300 | Proxy rotation interval (seconds) |
| `pool_size` | int | 10 | Target proxy pool size |
| `protocol` | str | None | Proxy protocol filter ("http", "https", None) |
| `country` | str | None | Country code filter (e.g., "US", "GB") |
| `max_speed_ms` | int | 2000 | Maximum proxy response time (ms) |
| `health_check_interval` | int | 60 | Health check interval (seconds) |
| `max_failures` | int | 3 | Max failures before marking proxy unhealthy |
| `custom_proxies` | list | [] | Custom proxy list |

### Provider Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `auto_proxy` | bool | False | Enable automatic proxy usage |
| `proxy_rotation` | bool | True | Use random vs sequential proxy selection |
| `proxies` | dict | None | Manual proxy config (overrides auto_proxy) |

## Features

### Automatic Proxy Fetching

The system uses ProxyFox to automatically fetch fresh proxies:

```python
# Fetches proxies with filters
proxies = proxyfox.get(
    count=10,
    protocol="http",
    country="US",
    max_speed_ms=2000
)
```

### Health Checking

Proxies are continuously monitored for health:

- **Background health checks** every 60 seconds (configurable)
- **Failure tracking** with automatic removal of unhealthy proxies
- **Automatic replacement** of failed proxies

### Proxy Rotation

Two rotation strategies:

1. **Sequential**: Round-robin through healthy proxies
2. **Random**: Random selection from healthy proxies

### Failure Handling

When a request fails:

1. **Mark proxy as failed** in the proxy manager
2. **Automatic retry** with a different proxy (if available)
3. **Proxy replacement** if failure threshold exceeded

## API Endpoints

### GET /v1/proxy/status

Returns proxy manager status and statistics.

**Response:**
```json
{
  "enabled": true,
  "stats": {
    "total_proxies": 10,
    "healthy_proxies": 8,
    "unhealthy_proxies": 2,
    "current_index": 3,
    "config": {
      "enabled": true,
      "auto_rotate": true,
      "pool_size": 10,
      "protocol": "http",
      "country": "US"
    }
  }
}
```

### POST /v1/proxy/refresh

Manually triggers proxy pool refresh.

**Response:**
```json
{
  "success": true,
  "message": "Proxy pool refresh initiated"
}
```

## Examples

### Complete Example

```python
from webscout.Provider.OPENAI.proxy_manager import ProxyConfig, initialize_global_proxy_manager
from webscout.Provider.OPENAI.deepinfra import DeepInfra

# Initialize global proxy manager
config = ProxyConfig(
    enabled=True,
    pool_size=5,
    country="US",
    max_speed_ms=1500
)
initialize_global_proxy_manager(config)

# Use with provider
client = DeepInfra()
response = client.chat.completions.create(
    model="deepseek-ai/DeepSeek-R1-Turbo",
    messages=[{"role": "user", "content": "Hello!"}],
    auto_proxy=True
)

print(response.choices[0].message.content)
```

### Custom Proxy List

```python
config = ProxyConfig(
    enabled=True,
    custom_proxies=[
        "http://proxy1.example.com:8080",
        "http://proxy2.example.com:8080",
        "socks5://proxy3.example.com:1080"
    ]
)
```

## Troubleshooting

### Common Issues

1. **ProxyFox not available**: Install with `pip install proxyfox`
2. **No healthy proxies**: Check proxy configuration and network connectivity
3. **Slow requests**: Adjust `max_speed_ms` filter or `health_check_interval`

### Debugging

Enable debug logging:

```python
import logging
logging.getLogger("webscout.Provider.OPENAI.proxy_manager").setLevel(logging.DEBUG)
```

### Performance Tips

- **Adjust pool size** based on request volume
- **Use country filtering** for better performance
- **Set appropriate speed limits** for your use case
- **Monitor proxy health** regularly

## Integration with Existing Code

The auto proxy functionality is designed to be backward compatible:

- **Existing code** continues to work without changes
- **Manual proxy configuration** takes precedence over auto proxy
- **Graceful fallback** when proxy manager is not available

## Security Considerations

- **Validate proxy sources** when using custom lists
- **Monitor proxy usage** for suspicious activity
- **Use HTTPS proxies** for sensitive data
- **Implement rate limiting** to avoid abuse

## Contributing

To add auto proxy support to a new provider:

1. **Inherit from BaseCompletions**
2. **Add proxy parameters** to the `create` method
3. **Use `get_proxy_config()`** helper method
4. **Handle proxy failures** with `handle_proxy_failure()`

Example:
```python
def create(self, *, auto_proxy=False, proxy_rotation=True, **kwargs):
    proxy_config = self.get_proxy_config(
        kwargs.get('proxies'), auto_proxy, proxy_rotation
    )
    
    try:
        # Make request with proxy_config
        response = requests.post(url, proxies=proxy_config, ...)
    except requests.RequestException:
        self.handle_proxy_failure(proxy_config)
        raise
```
