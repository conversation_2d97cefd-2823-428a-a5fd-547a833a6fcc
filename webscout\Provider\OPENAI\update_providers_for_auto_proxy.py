#!/usr/bin/env python3
"""
<PERSON>ript to update all OpenAI providers to support auto proxy functionality.

This script will:
1. <PERSON>an all provider files in the OPENAI directory
2. Update their __init__ methods to support auto proxy
3. Update their session/proxy handling code
4. Maintain backward compatibility
"""

import os
import re
import glob
from typing import List, <PERSON><PERSON>

def get_provider_files() -> List[str]:
    """Get list of all provider files in the OPENAI directory"""
    current_dir = os.path.dirname(__file__)
    pattern = os.path.join(current_dir, "*.py")
    files = glob.glob(pattern)
    
    # Filter out special files
    exclude_files = {
        "__init__.py", "base.py", "utils.py", "pydantic_imports.py", 
        "proxy_manager.py", "auto_proxy_example.py", "update_providers_for_auto_proxy.py"
    }
    
    provider_files = []
    for file_path in files:
        filename = os.path.basename(file_path)
        if filename not in exclude_files:
            provider_files.append(file_path)
    
    return provider_files

def analyze_provider_file(file_path: str) -> dict:
    """Analyze a provider file to understand its structure"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    analysis = {
        'file_path': file_path,
        'filename': os.path.basename(file_path),
        'has_openai_compatible_provider': 'OpenAICompatibleProvider' in content,
        'has_init_method': '__init__' in content,
        'has_proxies_param': 'proxies' in content,
        'has_session': 'session' in content,
        'needs_update': False,
        'init_method_line': None,
        'class_name': None
    }
    
    # Find class that inherits from OpenAICompatibleProvider
    class_pattern = r'class\s+(\w+)\s*\([^)]*OpenAICompatibleProvider[^)]*\):'
    class_match = re.search(class_pattern, content)
    if class_match:
        analysis['class_name'] = class_match.group(1)
    
    # Find __init__ method
    init_pattern = r'def __init__\s*\('
    init_match = re.search(init_pattern, content)
    if init_match:
        lines = content[:init_match.start()].count('\n')
        analysis['init_method_line'] = lines + 1
    
    # Check if already updated
    if 'auto_proxy' in content and 'proxy_config' in content:
        analysis['already_updated'] = True
    else:
        analysis['already_updated'] = False
        analysis['needs_update'] = analysis['has_openai_compatible_provider']
    
    return analysis

def generate_updated_init_signature(original_init: str) -> str:
    """Generate updated __init__ method signature with auto proxy support"""
    # Extract parameters from original init
    params_pattern = r'def __init__\s*\(\s*self\s*,([^)]*)\):'
    match = re.search(params_pattern, original_init)
    
    if not match:
        return original_init
    
    original_params = match.group(1).strip()
    
    # Check if auto_proxy parameters already exist
    if 'auto_proxy' in original_params:
        return original_init
    
    # Add auto proxy parameters
    auto_proxy_params = """
        auto_proxy: bool = True,
        proxy_config=None,"""
    
    # Insert auto proxy parameters before **kwargs if it exists, otherwise at the end
    if '**kwargs' in original_params:
        updated_params = original_params.replace('**kwargs', f'{auto_proxy_params}\n        **kwargs')
    else:
        if original_params:
            updated_params = f"{original_params},{auto_proxy_params}"
        else:
            updated_params = auto_proxy_params.strip()
    
    return f"def __init__(\n        self,{updated_params}\n    ):"

def generate_updated_init_body(original_body: str, class_name: str) -> str:
    """Generate updated __init__ method body with auto proxy support"""
    lines = original_body.split('\n')
    updated_lines = []
    
    # Find docstring end
    in_docstring = False
    docstring_end_line = 0
    
    for i, line in enumerate(lines):
        if '"""' in line:
            if not in_docstring:
                in_docstring = True
            else:
                in_docstring = False
                docstring_end_line = i
                break
    
    # Add super().__init__ call after docstring
    super_init_added = False
    session_proxy_updated = False
    
    for i, line in enumerate(lines):
        updated_lines.append(line)
        
        # Add super().__init__ call after docstring
        if i == docstring_end_line and not super_init_added:
            updated_lines.extend([
                "        # Initialize base class with auto proxy support",
                "        super().__init__(",
                "            proxies=proxies,",
                "            auto_proxy=auto_proxy,",
                "            proxy_config=proxy_config,",
                "            **kwargs",
                "        )",
                ""
            ])
            super_init_added = True
        
        # Update session proxy handling
        if 'self.session.proxies' in line and not session_proxy_updated:
            # Replace direct proxy assignment with auto proxy call
            indent = len(line) - len(line.lstrip())
            updated_lines.pop()  # Remove the original line
            updated_lines.extend([
                " " * indent + "# Get proxy configuration (auto or manual)",
                " " * indent + "proxy_config = self.get_proxies()",
                " " * indent + "if proxy_config:",
                " " * indent + "    self.session.proxies.update(proxy_config)"
            ])
            session_proxy_updated = True
    
    return '\n'.join(updated_lines)

def update_provider_file(file_path: str, analysis: dict) -> bool:
    """Update a provider file to support auto proxy"""
    if analysis['already_updated'] or not analysis['needs_update']:
        print(f"Skipping {analysis['filename']} - already updated or doesn't need update")
        return False
    
    print(f"Updating {analysis['filename']}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # This is a simplified update - in practice, you'd need more sophisticated parsing
    # For now, we'll just add a comment indicating the file needs manual update
    
    if '# AUTO PROXY UPDATE NEEDED' not in content:
        # Add a comment at the top indicating this file needs manual update
        lines = content.split('\n')
        
        # Find the first import or class line
        insert_line = 0
        for i, line in enumerate(lines):
            if line.strip().startswith(('import ', 'from ', 'class ')):
                insert_line = i
                break
        
        comment = [
            "# AUTO PROXY UPDATE NEEDED:",
            "# This provider needs to be updated to support auto proxy functionality.",
            "# Please update the __init__ method to include auto_proxy parameters",
            "# and call super().__init__ with proxy parameters.",
            "# See textpollinations.py or opkfc.py for examples.",
            ""
        ]
        
        lines[insert_line:insert_line] = comment
        
        updated_content = '\n'.join(lines)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True
    
    return False

def main():
    """Main function to update all providers"""
    print("Updating OpenAI providers for auto proxy support...")
    print("=" * 50)
    
    provider_files = get_provider_files()
    print(f"Found {len(provider_files)} provider files")
    
    updated_count = 0
    already_updated_count = 0
    needs_manual_update = []
    
    for file_path in provider_files:
        analysis = analyze_provider_file(file_path)
        
        print(f"\nAnalyzing {analysis['filename']}:")
        print(f"  - Has OpenAICompatibleProvider: {analysis['has_openai_compatible_provider']}")
        print(f"  - Has __init__ method: {analysis['has_init_method']}")
        print(f"  - Has proxies param: {analysis['has_proxies_param']}")
        print(f"  - Already updated: {analysis.get('already_updated', False)}")
        print(f"  - Needs update: {analysis['needs_update']}")
        
        if analysis.get('already_updated'):
            already_updated_count += 1
        elif analysis['needs_update']:
            if update_provider_file(file_path, analysis):
                updated_count += 1
                needs_manual_update.append(analysis['filename'])
    
    print("\n" + "=" * 50)
    print("Update Summary:")
    print(f"  - Total files: {len(provider_files)}")
    print(f"  - Already updated: {already_updated_count}")
    print(f"  - Marked for manual update: {updated_count}")
    print(f"  - No update needed: {len(provider_files) - already_updated_count - updated_count}")
    
    if needs_manual_update:
        print(f"\nFiles that need manual update:")
        for filename in needs_manual_update:
            print(f"  - {filename}")
        
        print("\nTo manually update these files:")
        print("1. Add auto_proxy and proxy_config parameters to __init__")
        print("2. Call super().__init__ with proxy parameters")
        print("3. Replace direct proxy assignment with self.get_proxies()")
        print("4. See textpollinations.py or opkfc.py for examples")

if __name__ == "__main__":
    main()
