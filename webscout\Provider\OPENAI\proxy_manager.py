"""
Auto Proxy Manager for Webscout OpenAI Providers

This module provides automatic proxy management using the ProxyFox library.
It handles proxy fetching, rotation, health checking, and integration with providers.
"""

import time
import random
import logging
import threading
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import requests

logger = logging.getLogger(__name__)

# Try to import ProxyFox, provide fallback if not available
try:
    import proxyfox
    PROXYFOX_AVAILABLE = True
except ImportError:
    PROXYFOX_AVAILABLE = False
    logger.warning("ProxyFox not available. Install with: pip install proxyfox")

@dataclass
class ProxyConfig:
    """Configuration for proxy management."""
    enabled: bool = False
    auto_rotate: bool = True
    rotation_interval: int = 300  # seconds
    pool_size: int = 10
    protocol: Optional[str] = None  # 'http', 'https', or None for both
    country: Optional[str] = None  # Country code (e.g., 'US', 'GB')
    max_speed_ms: Optional[int] = 2000  # Maximum response time in ms
    health_check_interval: int = 60  # seconds
    health_check_timeout: int = 10  # seconds
    max_failures: int = 3  # Max failures before removing proxy
    test_url: str = "https://httpbin.org/ip"
    custom_proxies: List[str] = field(default_factory=list)  # Custom proxy list

@dataclass
class ProxyInfo:
    """Information about a proxy."""
    address: str
    protocol: str = "http"
    country: Optional[str] = None
    speed_ms: Optional[int] = None
    failures: int = 0
    last_used: Optional[float] = None
    last_checked: Optional[float] = None
    is_healthy: bool = True

class ProxyManager:
    """Manages proxy pools with automatic rotation and health checking."""
    
    def __init__(self, config: ProxyConfig):
        self.config = config
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        self._lock = threading.RLock()
        self._health_check_thread = None
        self._rotation_thread = None
        self._running = False
        
        if config.enabled:
            self.start()
    
    def start(self):
        """Start the proxy manager."""
        if not self.config.enabled:
            return
            
        self._running = True
        self._initialize_proxies()
        
        # Start health checking thread
        if self.config.health_check_interval > 0:
            self._health_check_thread = threading.Thread(
                target=self._health_check_loop, daemon=True
            )
            self._health_check_thread.start()
        
        # Start rotation thread if auto-rotation is enabled
        if self.config.auto_rotate and self.config.rotation_interval > 0:
            self._rotation_thread = threading.Thread(
                target=self._rotation_loop, daemon=True
            )
            self._rotation_thread.start()
    
    def stop(self):
        """Stop the proxy manager."""
        self._running = False
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
        if self._rotation_thread:
            self._rotation_thread.join(timeout=5)
    
    def _initialize_proxies(self):
        """Initialize the proxy pool."""
        with self._lock:
            # Add custom proxies first
            for proxy_addr in self.config.custom_proxies:
                proxy_info = self._parse_proxy_address(proxy_addr)
                if proxy_info:
                    self.proxies.append(proxy_info)
            
            # Fetch proxies from ProxyFox if available
            if PROXYFOX_AVAILABLE and len(self.proxies) < self.config.pool_size:
                try:
                    needed = self.config.pool_size - len(self.proxies)
                    fetched_proxies = self._fetch_proxies_from_proxyfox(needed)
                    self.proxies.extend(fetched_proxies)
                except Exception as e:
                    logger.error(f"Failed to fetch proxies from ProxyFox: {e}")
            
            logger.info(f"Initialized proxy pool with {len(self.proxies)} proxies")
    
    def _fetch_proxies_from_proxyfox(self, count: int) -> List[ProxyInfo]:
        """Fetch proxies from ProxyFox."""
        proxies = []
        try:
            # Get proxies based on configuration
            kwargs = {}
            if self.config.protocol:
                kwargs['protocol'] = self.config.protocol
            if self.config.country:
                kwargs['country'] = self.config.country
            if self.config.max_speed_ms:
                kwargs['max_speed_ms'] = self.config.max_speed_ms
            
            # Fetch multiple proxies
            proxy_list = proxyfox.get(count, **kwargs)
            
            for proxy_addr in proxy_list:
                proxy_info = ProxyInfo(
                    address=proxy_addr,
                    protocol=self.config.protocol or "http",
                    country=self.config.country
                )
                proxies.append(proxy_info)
                
        except Exception as e:
            logger.error(f"Error fetching proxies from ProxyFox: {e}")
        
        return proxies
    
    def _parse_proxy_address(self, address: str) -> Optional[ProxyInfo]:
        """Parse a proxy address string."""
        try:
            # Handle different formats: ip:port, protocol://ip:port, etc.
            if "://" in address:
                protocol, addr = address.split("://", 1)
            else:
                protocol = self.config.protocol or "http"
                addr = address
            
            return ProxyInfo(address=addr, protocol=protocol)
        except Exception as e:
            logger.error(f"Failed to parse proxy address '{address}': {e}")
            return None
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """Get the next available proxy."""
        if not self.config.enabled or not self.proxies:
            return None
        
        with self._lock:
            # Filter healthy proxies
            healthy_proxies = [p for p in self.proxies if p.is_healthy]
            
            if not healthy_proxies:
                logger.warning("No healthy proxies available")
                return None
            
            # Get next proxy (round-robin)
            proxy = healthy_proxies[self.current_index % len(healthy_proxies)]
            self.current_index = (self.current_index + 1) % len(healthy_proxies)
            
            # Update usage timestamp
            proxy.last_used = time.time()
            
            # Format proxy for requests
            proxy_url = f"{proxy.protocol}://{proxy.address}"
            return {
                "http": proxy_url,
                "https": proxy_url
            }
    
    def get_random_proxy(self) -> Optional[Dict[str, str]]:
        """Get a random healthy proxy."""
        if not self.config.enabled or not self.proxies:
            return None
        
        with self._lock:
            healthy_proxies = [p for p in self.proxies if p.is_healthy]
            
            if not healthy_proxies:
                return None
            
            proxy = random.choice(healthy_proxies)
            proxy.last_used = time.time()
            
            proxy_url = f"{proxy.protocol}://{proxy.address}"
            return {
                "http": proxy_url,
                "https": proxy_url
            }
    
    def mark_proxy_failed(self, proxy_dict: Dict[str, str]):
        """Mark a proxy as failed."""
        if not proxy_dict:
            return
        
        proxy_url = proxy_dict.get("http") or proxy_dict.get("https")
        if not proxy_url:
            return
        
        # Extract address from URL
        try:
            if "://" in proxy_url:
                _, address = proxy_url.split("://", 1)
            else:
                address = proxy_url
        except:
            return
        
        with self._lock:
            for proxy in self.proxies:
                if proxy.address == address:
                    proxy.failures += 1
                    if proxy.failures >= self.config.max_failures:
                        proxy.is_healthy = False
                        logger.warning(f"Proxy {address} marked as unhealthy after {proxy.failures} failures")
                    break
    
    def _health_check_loop(self):
        """Background thread for health checking proxies."""
        while self._running:
            try:
                self._check_proxy_health()
                time.sleep(self.config.health_check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                time.sleep(10)  # Wait before retrying
    
    def _check_proxy_health(self):
        """Check health of all proxies."""
        if not self.proxies:
            return
        
        with ThreadPoolExecutor(max_workers=min(10, len(self.proxies))) as executor:
            futures = {
                executor.submit(self._test_proxy, proxy): proxy 
                for proxy in self.proxies
            }
            
            for future in as_completed(futures, timeout=30):
                proxy = futures[future]
                try:
                    is_healthy = future.result()
                    with self._lock:
                        proxy.last_checked = time.time()
                        if is_healthy:
                            proxy.failures = 0
                            proxy.is_healthy = True
                        else:
                            proxy.failures += 1
                            if proxy.failures >= self.config.max_failures:
                                proxy.is_healthy = False
                except Exception as e:
                    logger.debug(f"Health check failed for proxy {proxy.address}: {e}")
                    with self._lock:
                        proxy.failures += 1
                        if proxy.failures >= self.config.max_failures:
                            proxy.is_healthy = False
    
    def _test_proxy(self, proxy: ProxyInfo) -> bool:
        """Test if a proxy is working."""
        try:
            proxy_url = f"{proxy.protocol}://{proxy.address}"
            proxies = {"http": proxy_url, "https": proxy_url}
            
            response = requests.get(
                self.config.test_url,
                proxies=proxies,
                timeout=self.config.health_check_timeout,
                headers={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}
            )
            return response.status_code == 200
        except:
            return False
    
    def _rotation_loop(self):
        """Background thread for proxy rotation."""
        while self._running:
            try:
                time.sleep(self.config.rotation_interval)
                if self._running:
                    self._refresh_proxy_pool()
            except Exception as e:
                logger.error(f"Error in rotation loop: {e}")
    
    def _refresh_proxy_pool(self):
        """Refresh the proxy pool by fetching new proxies."""
        if not PROXYFOX_AVAILABLE:
            return
        
        try:
            # Remove unhealthy proxies
            with self._lock:
                healthy_count = sum(1 for p in self.proxies if p.is_healthy)
                
                if healthy_count < self.config.pool_size // 2:
                    # Fetch new proxies to replace unhealthy ones
                    needed = self.config.pool_size - healthy_count
                    new_proxies = self._fetch_proxies_from_proxyfox(needed)
                    
                    # Remove unhealthy proxies and add new ones
                    self.proxies = [p for p in self.proxies if p.is_healthy] + new_proxies
                    logger.info(f"Refreshed proxy pool: {len(new_proxies)} new proxies added")
        except Exception as e:
            logger.error(f"Failed to refresh proxy pool: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get proxy pool statistics."""
        with self._lock:
            total = len(self.proxies)
            healthy = sum(1 for p in self.proxies if p.is_healthy)
            
            return {
                "total_proxies": total,
                "healthy_proxies": healthy,
                "unhealthy_proxies": total - healthy,
                "current_index": self.current_index,
                "config": {
                    "enabled": self.config.enabled,
                    "auto_rotate": self.config.auto_rotate,
                    "pool_size": self.config.pool_size,
                    "protocol": self.config.protocol,
                    "country": self.config.country
                }
            }

# Global proxy manager instance
_global_proxy_manager: Optional[ProxyManager] = None

def get_global_proxy_manager() -> Optional[ProxyManager]:
    """Get the global proxy manager instance."""
    return _global_proxy_manager

def initialize_global_proxy_manager(config: ProxyConfig):
    """Initialize the global proxy manager."""
    global _global_proxy_manager
    if _global_proxy_manager:
        _global_proxy_manager.stop()
    _global_proxy_manager = ProxyManager(config)

def get_proxy() -> Optional[Dict[str, str]]:
    """Get a proxy from the global proxy manager."""
    manager = get_global_proxy_manager()
    return manager.get_proxy() if manager else None

def get_random_proxy() -> Optional[Dict[str, str]]:
    """Get a random proxy from the global proxy manager."""
    manager = get_global_proxy_manager()
    return manager.get_random_proxy() if manager else None

def mark_proxy_failed(proxy_dict: Dict[str, str]):
    """Mark a proxy as failed in the global proxy manager."""
    manager = get_global_proxy_manager()
    if manager:
        manager.mark_proxy_failed(proxy_dict)
