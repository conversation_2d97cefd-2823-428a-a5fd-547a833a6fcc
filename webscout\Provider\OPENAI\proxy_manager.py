"""
Auto Proxy Manager for Webscout OpenAI Providers

This module provides automatic proxy functionality using proxyfox library.
It fetches, validates, and rotates proxies automatically for all OpenAI-compatible providers.
"""

import logging
import random
import time
import threading
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
import requests

logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """Configuration for proxy management"""
    enabled: bool = True
    auto_rotate: bool = True
    rotation_interval: int = 300  # 5 minutes
    max_retries: int = 3
    timeout: int = 10
    protocol: str = "http"  # "http", "https", or "both"
    country: Optional[str] = None
    max_speed_ms: int = 2000
    pool_size: int = 10
    validate_proxies: bool = True
    fallback_to_direct: bool = True

class ProxyManager:
    """
    Manages automatic proxy fetching, validation, and rotation using proxyfox.
    """
    
    def __init__(self, config: Optional[ProxyConfig] = None):
        """
        Initialize the ProxyManager.
        
        Args:
            config: ProxyConfig object with proxy settings
        """
        self.config = config or ProxyConfig()
        self._proxies: List[str] = []
        self._current_proxy_index = 0
        self._last_rotation = 0
        self._lock = threading.Lock()
        self._proxyfox_available = False
        self._proxy_pool = None
        
        # Try to import proxyfox
        try:
            import proxyfox
            self._proxyfox = proxyfox
            self._proxyfox_available = True
            logger.info("ProxyFox library loaded successfully")
        except ImportError:
            logger.warning("ProxyFox library not available. Install with: pip install proxyfox")
            self._proxyfox_available = False
        
        # Initialize proxy pool if proxyfox is available
        if self._proxyfox_available and self.config.enabled:
            self._initialize_proxy_pool()
    
    def _initialize_proxy_pool(self):
        """Initialize the proxy pool using proxyfox"""
        if not self._proxyfox_available:
            return
        
        try:
            # Create auto-updating proxy pool
            pool_kwargs = {
                'size': self.config.pool_size,
                'refresh_interval': self.config.rotation_interval,
            }
            
            # Add optional filters
            if self.config.protocol in ['http', 'https']:
                pool_kwargs['protocol'] = self.config.protocol
            if self.config.country:
                pool_kwargs['country'] = self.config.country
            if self.config.max_speed_ms:
                pool_kwargs['max_speed_ms'] = self.config.max_speed_ms
            
            self._proxy_pool = self._proxyfox.create_pool(**pool_kwargs)
            logger.info(f"Initialized proxy pool with {self.config.pool_size} proxies")
            
            # Get initial proxies
            self._refresh_proxies()
            
        except Exception as e:
            logger.error(f"Failed to initialize proxy pool: {e}")
            self._proxy_pool = None
    
    def _refresh_proxies(self):
        """Refresh the proxy list from the pool"""
        if not self._proxy_pool:
            return
        
        try:
            with self._lock:
                new_proxies = self._proxy_pool.all()
                if new_proxies:
                    self._proxies = new_proxies
                    self._current_proxy_index = 0
                    self._last_rotation = time.time()
                    logger.info(f"Refreshed proxy list with {len(self._proxies)} proxies")
                else:
                    logger.warning("No proxies available from pool")
        except Exception as e:
            logger.error(f"Failed to refresh proxies: {e}")
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """
        Get a proxy for use in requests.
        
        Returns:
            Dict with proxy configuration or None if no proxy available
        """
        if not self.config.enabled or not self._proxyfox_available:
            return None
        
        # Check if we need to rotate proxies
        if (self.config.auto_rotate and 
            time.time() - self._last_rotation > self.config.rotation_interval):
            self._refresh_proxies()
        
        with self._lock:
            if not self._proxies:
                if self._proxy_pool:
                    # Try to get a fresh proxy
                    try:
                        proxy = self._proxy_pool.get()
                        if proxy:
                            return self._format_proxy(proxy)
                    except Exception as e:
                        logger.error(f"Failed to get proxy from pool: {e}")
                return None
            
            # Get current proxy and rotate to next
            proxy = self._proxies[self._current_proxy_index]
            self._current_proxy_index = (self._current_proxy_index + 1) % len(self._proxies)
            
            return self._format_proxy(proxy)
    
    def _format_proxy(self, proxy: str) -> Dict[str, str]:
        """
        Format proxy string into requests-compatible dict.
        
        Args:
            proxy: Proxy string in format "ip:port"
            
        Returns:
            Dict with http and https proxy configuration
        """
        if not proxy:
            return {}
        
        # Handle different proxy formats
        if '://' not in proxy:
            proxy = f"http://{proxy}"
        
        return {
            'http': proxy,
            'https': proxy
        }
    
    def validate_proxy(self, proxy_dict: Dict[str, str], test_url: str = "http://httpbin.org/ip") -> bool:
        """
        Validate if a proxy is working.
        
        Args:
            proxy_dict: Proxy configuration dict
            test_url: URL to test proxy against
            
        Returns:
            True if proxy is working, False otherwise
        """
        if not proxy_dict or not self.config.validate_proxies:
            return True
        
        try:
            response = requests.get(
                test_url,
                proxies=proxy_dict,
                timeout=self.config.timeout
            )
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"Proxy validation failed: {e}")
            return False
    
    def get_validated_proxy(self) -> Optional[Dict[str, str]]:
        """
        Get a validated proxy that's confirmed to be working.
        
        Returns:
            Dict with proxy configuration or None if no working proxy found
        """
        for _ in range(self.config.max_retries):
            proxy = self.get_proxy()
            if not proxy:
                break
            
            if self.validate_proxy(proxy):
                return proxy
            
            logger.debug("Proxy validation failed, trying next proxy")
        
        if self.config.fallback_to_direct:
            logger.info("No working proxy found, falling back to direct connection")
            return None
        else:
            logger.warning("No working proxy found and fallback disabled")
            return {}
    
    def get_manual_proxy(self, proxy_input: Union[str, Dict[str, str]]) -> Dict[str, str]:
        """
        Convert manual proxy input to standard format.
        
        Args:
            proxy_input: Manual proxy configuration
            
        Returns:
            Formatted proxy dict
        """
        if isinstance(proxy_input, dict):
            return proxy_input
        elif isinstance(proxy_input, str):
            return self._format_proxy(proxy_input)
        else:
            return {}
    
    def merge_proxies(self, manual_proxies: Optional[Union[str, Dict[str, str]]] = None) -> Optional[Dict[str, str]]:
        """
        Merge manual proxy configuration with auto proxy.
        Manual proxies take precedence over auto proxies.
        
        Args:
            manual_proxies: Manual proxy configuration
            
        Returns:
            Final proxy configuration to use
        """
        # If manual proxies are provided, use them
        if manual_proxies:
            return self.get_manual_proxy(manual_proxies)
        
        # Otherwise use auto proxy if enabled
        if self.config.enabled:
            return self.get_validated_proxy()
        
        return None
    
    def is_available(self) -> bool:
        """Check if proxy functionality is available"""
        return self._proxyfox_available
    
    def get_stats(self) -> Dict[str, any]:
        """Get proxy manager statistics"""
        with self._lock:
            return {
                'enabled': self.config.enabled,
                'proxyfox_available': self._proxyfox_available,
                'proxy_count': len(self._proxies),
                'current_index': self._current_proxy_index,
                'last_rotation': self._last_rotation,
                'pool_active': self._proxy_pool is not None
            }

# Global proxy manager instance
_global_proxy_manager: Optional[ProxyManager] = None

def get_global_proxy_manager() -> ProxyManager:
    """Get or create the global proxy manager instance"""
    global _global_proxy_manager
    if _global_proxy_manager is None:
        _global_proxy_manager = ProxyManager()
    return _global_proxy_manager

def set_global_proxy_config(config: ProxyConfig):
    """Set global proxy configuration"""
    global _global_proxy_manager
    _global_proxy_manager = ProxyManager(config)
