#!/usr/bin/env python3
"""
Example demonstrating auto proxy functionality in Webscout OpenAI providers.

This script shows how to:
1. Use auto proxy with default settings
2. Configure custom proxy settings
3. Use manual proxies alongside auto proxy
4. Check proxy statistics
"""

import sys
import os

# Add the webscout directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from webscout.Provider.OPENAI.proxy_manager import ProxyConfig, ProxyManager
from webscout.Provider.OPENAI.textpollinations import TextPollinations

def example_1_basic_auto_proxy():
    """Example 1: Basic auto proxy usage"""
    print("=== Example 1: Basic Auto Proxy ===")
    
    # Create a provider with auto proxy enabled (default)
    client = TextPollinations()
    
    # Check proxy stats
    stats = client.get_proxy_stats()
    print(f"Proxy stats: {stats}")
    
    # Get current proxy configuration
    proxies = client.get_proxies()
    print(f"Current proxies: {proxies}")
    
    # Test a simple request (commented out to avoid actual API calls)
    # response = client.chat.completions.create(
    #     model="openai-gpt-4o-mini",
    #     messages=[{"role": "user", "content": "Hello!"}]
    # )
    # print(f"Response: {response.choices[0].message.content}")

def example_2_custom_proxy_config():
    """Example 2: Custom proxy configuration"""
    print("\n=== Example 2: Custom Proxy Configuration ===")
    
    # Create custom proxy configuration
    proxy_config = ProxyConfig(
        enabled=True,
        auto_rotate=True,
        rotation_interval=60,  # Rotate every minute
        max_retries=5,
        timeout=15,
        protocol="http",  # Only HTTP proxies
        country="US",  # Only US proxies
        max_speed_ms=1000,  # Fast proxies only
        pool_size=20,  # Larger proxy pool
        validate_proxies=True,
        fallback_to_direct=True
    )
    
    # Create provider with custom config
    client = TextPollinations(
        auto_proxy=True,
        proxy_config=proxy_config
    )
    
    stats = client.get_proxy_stats()
    print(f"Custom proxy stats: {stats}")

def example_3_manual_proxies():
    """Example 3: Manual proxy configuration"""
    print("\n=== Example 3: Manual Proxies ===")
    
    # Manual proxy as string
    client1 = TextPollinations(
        proxies="http://proxy.example.com:8080",
        auto_proxy=False  # Disable auto proxy
    )
    
    proxies1 = client1.get_proxies()
    print(f"Manual proxy (string): {proxies1}")
    
    # Manual proxy as dict
    client2 = TextPollinations(
        proxies={
            "http": "http://proxy.example.com:8080",
            "https": "https://proxy.example.com:8080"
        },
        auto_proxy=False
    )
    
    proxies2 = client2.get_proxies()
    print(f"Manual proxy (dict): {proxies2}")

def example_4_mixed_configuration():
    """Example 4: Mixed manual and auto proxy"""
    print("\n=== Example 4: Mixed Configuration ===")
    
    # Start with auto proxy
    client = TextPollinations(auto_proxy=True)
    
    print("Initial configuration:")
    print(f"Auto proxy enabled: {client.auto_proxy_enabled}")
    print(f"Current proxies: {client.get_proxies()}")
    
    # Switch to manual proxy
    client.set_manual_proxies("http://manual.proxy.com:8080")
    print(f"\nAfter setting manual proxy: {client.get_proxies()}")
    
    # Disable auto proxy
    client.enable_auto_proxy(False)
    print(f"After disabling auto proxy: {client.get_proxy_stats()}")
    
    # Re-enable auto proxy
    client.enable_auto_proxy(True)
    print(f"After re-enabling auto proxy: {client.get_proxy_stats()}")

def example_5_proxy_manager_direct():
    """Example 5: Using ProxyManager directly"""
    print("\n=== Example 5: Direct ProxyManager Usage ===")
    
    # Create proxy manager with custom config
    config = ProxyConfig(
        enabled=True,
        pool_size=5,
        protocol="https",
        validate_proxies=True
    )
    
    proxy_manager = ProxyManager(config)
    
    if proxy_manager.is_available():
        print("ProxyFox is available!")
        
        # Get a proxy
        proxy = proxy_manager.get_proxy()
        print(f"Got proxy: {proxy}")
        
        # Get a validated proxy
        validated_proxy = proxy_manager.get_validated_proxy()
        print(f"Validated proxy: {validated_proxy}")
        
        # Get stats
        stats = proxy_manager.get_stats()
        print(f"Manager stats: {stats}")
    else:
        print("ProxyFox is not available. Install with: pip install proxyfox")

def main():
    """Run all examples"""
    print("Webscout Auto Proxy Examples")
    print("=" * 40)
    
    try:
        example_1_basic_auto_proxy()
        example_2_custom_proxy_config()
        example_3_manual_proxies()
        example_4_mixed_configuration()
        example_5_proxy_manager_direct()
        
        print("\n" + "=" * 40)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
